<template>
  <div class="personal-memo-container">
    <div class="memo-title">个人备忘录</div>
    <div class="memo-content">
      <div ref="tagsContainer" class="memo-tags-container">
        <div
          v-for="(memory, index) in memories"
          :key="index"
          class="memo-tag"
          @click="handleMemoItemClick(memory)"
        >
          <span class="memo-text">{{ memory.description_text }}</span>
          <span class="memo-delete-btn" @click="handleDeleteClick(memory)">×</span>
        </div>
        <div v-if="memories.length === 0 && !isLoading && hasReceived" class="empty-memo">
          暂无备忘录内容
        </div>
        <div v-if="isLoading" class="empty-memo">
          加载中...
        </div>
      </div>
    </div>
    <div class="memo-add-btn" @click="handleAddClick">
      <DeleteIcon :size="14" color="white" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';
import { getPersonMemories, type IEvent } from '@/apis/memory';
import DeleteIcon from '@/assets/icons/DeleteIcon.vue';

// Props定义
interface IProps {
  userId: string;
  personId: string;
}

const props = defineProps<IProps>();

// Emits定义
const emit = defineEmits<{
  addPersonMemo: [];
  editPersonMemo: [event: IEvent];
  deleteMemo: [memory: IEvent];
}>();



// 响应式数据
const memories = ref<IEvent[]>([]);
const isLoading = ref(false);
const hasReceived = ref(false);
const tagsContainer = ref<HTMLElement>();

// 获取记忆数据
const fetchMemories = async () => {
  try {
    isLoading.value = true;
    console.log('📤 [PersonalMemo] 开始获取记忆数据:', {
      userId: props.userId,
      personId: props.personId,
    });

    const response = await getPersonMemories({
      user_id: props.userId,
      person_id: props.personId,
    });

    if (response.result === 'success' && response.events) {
      memories.value = response.events;
      console.log('✅ [PersonalMemo] 记忆数据获取成功:', memories.value);
    } else {
      console.warn('⚠️ [PersonalMemo] 记忆数据获取失败或无数据');
      memories.value = [];
    }
  } catch (error) {
    console.error('❌ [PersonalMemo] 获取记忆数据失败:', error);
    memories.value = [];
  } finally {
    isLoading.value = false;
    hasReceived.value = true;
  }
};

// 处理添加按钮点击事件
const handleAddClick = () => {
  console.log('个人备忘录添加按钮被点击');
  emit('addPersonMemo');
};

// 处理备忘录项点击事件
const handleMemoItemClick = (memory: IEvent) => {
  console.log('个人备忘录项被点击:', memory);
  emit('editPersonMemo', memory);
};

// 处理删除按钮点击事件
const handleDeleteClick = (memory: IEvent) => {
  console.log('个人备忘录删除按钮被点击:', memory);
  emit('deleteMemo', memory);
};

// 监听props变化
watch([() => props.userId, () => props.personId], () => {
  if (props.userId && props.personId) {
    void fetchMemories();
  }
}, { immediate: true });

// 组件挂载时获取数据
onMounted(() => {
  if (props.userId && props.personId) {
    void fetchMemories();
  }
});

// 暴露刷新方法给父组件调用
defineExpose({
  fetchMemories,
});

// 暴露刷新方法给父组件
defineExpose({
  fetchMemories,
});
</script>

<style lang="scss" scoped>
.personal-memo-container {
  height: 80px;
  width: 100%;
  background: #fff9c4; // 浅黄色底
  border-radius: 12px;
  display: flex;
  align-items: center;
  padding: 16px;
  box-sizing: border-box;
  position: relative;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .memo-title {
    color: #f39c12; // 黄色标题
    font-size: 26px;
    font-weight: 600;
    white-space: nowrap;
    margin-right: 16px;
  }

  .memo-content {
    flex: 1;
    height: 100%;
    overflow: hidden;

    .memo-tags-container {
      display: flex;
      align-items: center;
      height: 100%;
      overflow-x: auto;
      overflow-y: hidden;
      gap: 12px;
      padding: 4px 0;

      // 隐藏滚动条但保持滚动功能
      scrollbar-width: none; // Firefox
      -ms-overflow-style: none; // IE/Edge
      &::-webkit-scrollbar {
        display: none; // Chrome/Safari
      }

      .memo-tag {
        background: #fdeebd; // 圆角浅黄色底
        border-radius: 10px;
        padding: 8px 16px;
        font-size: 26px;
        color: #856404;
        white-space: nowrap;
        flex-shrink: 0;
        border: 1px solid #ffeaa7;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        transition: all 0.2s ease;
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 8px;

        .memo-text {
          flex: 1;
        }

        .memo-delete-btn {
          color: #f39c12;
          font-size: 24px;
          font-weight: bold;
          cursor: pointer;
          padding: 2px 6px;
          border-radius: 50%;
          transition: all 0.2s ease;
          line-height: 1;
        }
      }

      .empty-memo {
        color: #d4a574;
        font-size: 24px;
        font-style: italic;
        white-space: nowrap;
      }
    }
  }

  .memo-add-btn {
    width: 40px;
    height: 40px;
    background: #f39c12;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    flex-shrink: 0;
    margin-left: 12px;
    
    .add-icon {
      color: white;
      font-size: 24px;
      font-weight: bold;
    }
  }
}
</style>
